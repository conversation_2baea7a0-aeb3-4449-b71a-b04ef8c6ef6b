{
  "pages": [
    {
      "name": "index",
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "name": "busIndex",
      "path": "pages/index/bus", // 当设置为综合体育场馆模式时 用以门店首页的渲染 原首页用以渲染商家首页
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/mall/list",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/mall/list-new",
      "style": {
        "navigationBarTitleText": "积分商城"
      }
    },
    {
      "path": "pages/mall/detail",
      "style": {
        "navigationBarTitleText": "商品详情"
      }
    },
    {
      "path": "pages/login",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "style": {
        "navigationBarTitleText": "场馆选择"
      },
      "path": "pages/busSelect"
    },
    {
      "style": {
        "navigationBarTitleText": "权限设置"
      },
      "path": "pages/infoAuthor"
    },
    {
      "style": {
        "navigationStyle": "custom"
      },
      "path": "pages/bus/detail"
    },
    {
      "style": {
        "navigationBarTitleText": "留言咨询"
      },
      "path": "pages/bus/feedback"
    },
    {
      "style": {
        "navigationBarTitleText": "扫码签到"
      },
      "path": "pages/train/signin"
    },
    {
      "style": {
        "navigationBarTitleText": "提示"
      },
      "path": "pages/train/signResult"
    },
    {
      "style": {
        "navigationStyle": "custom"
      },
      "path": "pages/train/trainOver"
    },
    {
      "style": {
        "navigationBarTitleText": "训练详情"
      },
      "path": "pages/train/trainDetail"
    },
    {
      "style": {
        "navigationStyle": "custom"
      },
      "path": "pages/class/class"
    },
    {
      "style": {
        "navigationBarTitleText": "我的"
      },
      "path": "pages/my/index"
    },
    {
      "style": {
        "navigationBarTitleText": "个人信息"
      },
      "path": "pages/my/info"
    },
    {
      "path": "pages/my/card",
      "style": {
        "navigationBarTitleText": "我的会员卡"
      }
    },
    {
      "path": "pages/my/coupon",
      "style": {
        "navigationBarTitleText": "我的折扣券"
      }
    },
    {
      "style": {
        "navigationBarTitleText": "预约记录"
      },
      "path": "pages/my/reserveRecord"
    },
    {
      "style": {
        "navigationBarTitleText": "体测详情"
      },
      "path": "pages/my/resultDetail"
    },
    {
      "style": {
        "navigationBarTitleText": "维塑结果页面"
      },
      "path": "pages/my/result"
    },
    {
      "style": {
        "navigationBarTitleText": "身体信息填写"
      },
      "path": "pages/train/extraInfo"
    },
    {
      "style": {
        "navigationBarTitleText": "体测曲线走势",
        "enablePullDownRefresh": true
      },
      "path": "pages/physicalExamination/physicalExamination"
    },
    {
      "style": {
        "navigationBarTitleText": "私教预约"
      },
      "path": "pages/class/ptReserve"
    },
    {
      "style": {
        "navigationBarTitleText": "私教预约详情"
      },
      "path": "pages/class/ptReserveDetai"
    },
    {
      "style": {
        "navigationBarTitleText": "团课预约"
      },
      "path": "pages/class/openClassReserve"
    },
    {
      "style": {
        "navigationBarTitleText": "团课预约详情"
      },
      "path": "pages/class/openClassReserveDetail"
    },
    {
      "style": {
        "navigationBarTitleText": "预约用卡"
      },
      "path": "pages/class/chargeCardSelect"
    },
    {
      "style": {
        "navigationBarTitleText": "团操课预约协议"
      },
      "path": "pages/class/protocol"
    },
    {
      "style": {
        "navigationBarTitleText": "团课详情"
      },
      "path": "pages/class/openClassDetail"
    },
    {
      "style": {
        "navigationBarTitleText": "评价"
      },
      "path": "pages/class/comment"
    },
    {
      "style": {
        "navigationBarTitleText": "教练"
      },
      "path": "pages/coach/list"
    },
    {
      "path": "pages/coach/detail",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "style": {
        "navigationBarTitleText": "卡课列表"
      },
      "path": "pages/card/list"
    },
    {
      "style": {
        "navigationBarTitleText": "活动卡课"
      },
      "path": "pages/card/pointCard"
    },
    {
      "style": {
        "navigationBarTitleText": "订单详情"
      },
      "path": "pages/card/buyCardDetail"
    },
    {
      "style": {
        "navigationBarTitleText": "购买成功"
      },
      "path": "pages/card/buyCardSuccess"
    },
    {
      "style": {
        "navigationBarTitleText": "卡课详情"
      },
      "path": "pages/card/cardDetail"
    },
    {
      "style": {
        "navigationBarTitleText": "适用门店"
      },
      "path": "pages/card/supportBusList"
    },
    {
      "style": {
        "navigationBarTitleText": "选择优惠"
      },
      "path": "pages/card/couponList"
    },
    {
      "style": {
        "navigationBarTitleText": "会员卡服务条款"
      },
      "path": "pages/card/protocol"
    },
    {
      "style": {
        "navigationBarTitleText": "场内活动"
      },
      "path": "pages/activity/index"
    },
    {
      "style": {
        "navigationBarTitleText": "活动详情"
      },
      "path": "pages/activity/activityDetail"
    },
    {
      "style": {
        "navigationBarTitleText": "活动详情"
      },
      "path": "pages/activity/refundActivity"
    },
    {
      "style": {
        "navigationStyle": "custom"
        // "navigationBarTitleText": "体验卡领取" // 好友赠送
      },
      "path": "pages/bonuses/giftCard"
    },
    {
      "style": {
        "navigationStyle": "custom"
      },
      "path": "pages/bonuses/giftCoupon"
    },
    {
      "style": {
        "navigationStyle": "custom"
        // "navigationBarTitleText": "体验卡领取" // 会籍赠送
      },
      "path": "pages/my/expCardConfirm"
    },
    {
      "style": {
        "navigationStyle": "custom"
      },
      "path": "pages/my/point"
    },
    {
      "style": {
        "navigationBarTitleText": "详情"
      },
      "path": "pages/my/pointTaskDetail"
    },
    {
      "style": {
        "navigationStyle": "custom"
      },
      "path": "pages/my/pointTask"
    },
    {
      "path": "pages/invitation/detail"
    },
    {
      "style": {
        "navigationBarTitleText": "凭证"
      },
      "path": "pages/my/ticket"
    },
    {
      "style": {
        "navigationBarTitleText": "凭证"
      },
      "path": "pages/my/ticket-new"
    },
    {
      "style": {
        "navigationBarTitleText": "入场凭证"
      },
      "path": "pages/my/ticketDetail"
    },
    {
      "style": {
        "navigationBarTitleText": "订场/散场"
      },
      "path": "pages/stadium/detail"
    },
    {
      "style": {
        "navigationBarTitleText": "适用场地"
      },
      "path": "pages/stadium/applicableSpace"
    },
    {
      "style": {
        "navigationBarTitleText": "预订场地"
      },
      "path": "pages/stadium/choose"
    },
    {
      "style": {
        "navigationBarTitleText": "支付完成"
      },
      "path": "pages/stadium/orderSucceed"
    },
    {
      "style": {
        "navigationBarTitleText": "散场购票"
      },
      "path": "pages/stadium/buyTicket"
    },
    {
      "style": {
        "navigationBarTitleText": "预订场地"
      },
      "path": "pages/stadium/buySpace"
    },
    {
      "style": {
        "navigationBarTitleText": "选择教练"
      },
      "path": "pages/stadium/selectCoach"
    },
    {
      "style": {
        "navigationBarTitleText": "散场票"
      },
      "path": "pages/stadium/ticketList"
    },
    {
      "style": {
        "navigationBarTitleText": "邀约列表"
      },
      "path": "pages/stadium/aboutBall"
    },
    {
      "style": {
        "navigationBarTitleText": "邀约详情"
      },
      "path": "pages/stadium/ballDetail"
    },
    {
      "style": {
        "navigationBarTitleText": "邀约发布"
      },
      "path": "pages/stadium/ballRelease"
    },
    {
      "style": {
        "navigationBarTitleText": "发起邀约"
      },
      "path": "pages/stadium/toInvite"
    },
    {
      "style": {
        "navigationBarTitleText": "场地选择"
      },
      "path": "pages/stadium/inviteChoose"
    },
    {
      "style": {
        "navigationBarTitleText": "发布成功"
      },
      "path": "pages/stadium/releaseSuccess"
    },
    {
      "style": {
        "navigationBarTitleText": "刷掌授权",
        "navigationStyle": "custom"
      },
      "path": "pages/wxPay/wxPayPalm"
    },
    {
      "style": {
        "navigationBarTitleText": "刷掌服务"
      },
      "path": "pages/wxPay/palmManage"
    },
    {
      "style": {
        "navigationBarTitleText": "刷掌绑定成功",
        "navigationStyle": "custom"
      },
      "path": "pages/wxPay/bindPalmPaySucceed"
    },
    {
      "style": {
        "navigationBarTitleText": "自助取票"
      },
      "path": "pages/ticket/index"
    },
    {
      "style": {
        "navigationBarTitleText": "连续包月服务"
      },
      "path": "pages/payscore/list"
    },
    {
      "style": {
        "navigationBarTitleText": "微信连续包月服务"
      },
      "path": "pages/payscore/detail"
    },
    {
      "style": {
        "navigationBarTitleText": "查看会员购买协议"
      },
      "path": "pages/payscore/protocol"
    },
    {
      "style": {
        "navigationBarTitleText": "查看包月期限"
      },
      "path": "pages/payscore/subscribe"
    },
    {
      "style": {
        "navigationBarTitleText": "连续包月服务购买结果"
      },
      "path": "pages/payscore/succeed"
    },
    {
      "style": {
        "navigationBarTitleText": "入场票赠送"
      },
      "path": "pages/receiveCard/receiveCard"
    }
  ],
  "subPackages": [
    {
      "root": "packageMy",
      "pages": [
        {
          "style": {
            "navigationBarTitleText": "帮助中心"
          },
          "path": "agreement/index"
        },
        {
          "style": {
            "navigationBarTitleText": "隐私政策"
          },
          "path": "agreement/private"
        },
        {
          "style": {
            "navigationBarTitleText": "用户协议"
          },
          "path": "agreement/user"
        },
        {
          "style": {
            "navigationBarTitleText": "场馆协议"
          },
          "path": "agreement/busProtocol"
        },
        {
          "style": {
            "navigationBarTitleText": "进店指引"
          },
          "path": "agreement/guide"
        },
        {
          "style": {
            "navigationBarTitleText": "隐私政策"
          },
          "path": "agreement/esign/private"
        },
        {
          "style": {
            "navigationBarTitleText": "认证服务协议"
          },
          "path": "agreement/esign/certification"
        },
        {
          "style": {
            "navigationBarTitleText": "数字证书服务协议"
          },
          "path": "agreement/esign/digital"
        },
        {
          "style": {
            "navigationBarTitleText": "人脸采集"
          },
          "path": "my/face"
        },
        {
          "style": {
            "navigationBarTitleText": "人脸采集"
          },
          "path": "my/faceSucceed"
        },
        {
          "style": {
            "navigationBarTitleText": "人脸授权"
          },
          "path": "my/faceAuth"
        },
        {
          "style": {
            "navigationBarTitleText": "人脸采集"
          },
          "path": "my/cropper"
        },
        {
          "style": {
            "navigationBarTitleText": "开柜密码"
          },
          "path": "my/pwd"
        },
        {
          "style": {
            "navigationBarTitleText": "我的合同"
          },
          "path": "my/contract"
        },
        {
          "path": "my/contractDetail"
        },
        {
          "style": {
            "navigationBarTitleText": "身份验证"
          },
          "path": "my/certification"
        },
        {
          "style": {
            "navigationBarTitleText": "认证结果"
          },
          "path": "my/faceResult"
        },
        {
          "style": {
            "navigationBarTitleText": "合同签字"
          },
          "path": "my/autograph"
        },
        {
          "style": {
            "navigationBarTitleText": "身份验证"
          },
          "path": "my/esigMsg"
        },
        {
          "style": {
            "navigationBarTitleText": "开票申请"
          },
          "path": "my/invoiceAdd"
        },
        {
          "style": {
            "navigationBarTitleText": "我的发票"
          },
          "path": "my/invoiceRecord"
        },
        {
          "style": {
            "navigationBarTitleText": "我的二维码"
          },
          "path": "my/qrCode"
        },
        {
          "style": {
            "navigationBarTitleText": "请假记录"
          },
          "path": "leave/list"
        },
        {
          "style": {
            "navigationBarTitleText": "请假记录"
          },
          "path": "leave/request"
        },
        {
          "style": {
            "navigationBarTitleText": "扫码选柜号"
          },
          "path": "cabinet/list"
        },
        {
          "style": {
            "navigationBarTitleText": "租用储物柜"
          },
          "path": "cabinet/info"
        },
        {
          "style": {
            "navigationBarTitleText": "智能柜使用记录"
          },
          "path": "cabinet/record"
        },
        {
          "style": {
            "navigationBarTitleText": "租用淋浴间"
          },
          "path": "shower/rent"
        },
        {
          "style": {
            "navigationBarTitleText": "租用淋浴间"
          },
          "path": "shower/cardChoose"
        },
        {
          "style": {
            "navigationBarTitleText": "信息确认"
          },
          "path": "my/depositConfirm"
        },
        {
          "style": {
            "navigationBarTitleText": "储值卡汇总"
          },
          "path": "recharge/index"
        },
        {
          "style": {
            "navigationBarTitleText": "储值卡充值"
          },
          "path": "recharge/detail"
        },
        {
          "style": {
            "navigationBarTitleText": "分享记录"
          },
          "path": "cardshare/list"
        },
        {
          "style": {
            "navigationBarTitleText": "会员卡分享"
          },
          "path": "cardshare/add"
        },
        {
          "style": {
            "navigationBarTitleText": "会员卡赠送"
          },
          "path": "cardshare/detail"
        },
        {
          "style": {
            "navigationBarTitleText": "红包列表"
          },
          "path": "redBag/list"
        },
        {
          "style": {
            "navigationBarTitleText": "红包记录"
          },
          "path": "redBag/log"
        },
        {
          "style": {
            "navigationBarTitleText": "红包领取",
            "navigationBarBackgroundColor": "#d8593f",
            "navigationBarTextStyle": "white"
          },
          "path": "redBag/open"
        },
        {
          "style": {
            "navigationBarTitleText": "开奖结果",
            "navigationBarBackgroundColor": "#d8593f",
            "navigationBarTextStyle": "white"
          },
          "path": "redBag/result"
        },
        {
          "style": {
            "navigationBarTitleText": "红包分享",
            "navigationBarBackgroundColor": "#d8593f",
            "navigationBarTextStyle": "white"
          },
          "path": "redBag/share"
        },
        {
          "style": {
            "navigationStyle": "custom"
            // "navigationBarTitleText": "体验卡领取" // 好友赠送
          },
          "path": "redBag/giftCard"
        },
        {
          "style": {
            "navigationBarTitleText": "清洁",
            "enablePullDownRefresh": true
          },
          "path": "clean/index"
        },
        {
          "style": {
            "navigationBarTitleText": "跳转"
          },
          "path": "jump/jump"
        },
        {
          "style": {
            "navigationBarTitleText": "团购核销"
          },
          "path": "thirdParty/verify"
        },
        {
          "style": {
            "navigationBarTitleText": "核销结果"
          },
          "path": "thirdParty/verifyResult"
        },
        {
          "style": {
            "navigationBarTitleText": "场馆选择"
          },
          "path": "thirdParty/busSelect"
        }
      ]
    },
    {
      "root": "packagePt",
      "pages": [
        {
          "style": {
            "navigationBarTitleText": "课程评估报告"
          },
          "path": "ptConfirm/ptReport"
        },
        {
          "style": {
            "navigationBarTitleText": "课程确认"
          },
          "path": "ptConfirm/ptConfirm"
        },
        {
          "style": {
            "navigationBarTitleText": "确认到场"
          },
          "path": "ptConfirm/ptArrival"
        }
      ]
    }
  ],
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
      "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue",
      "^Theme(.*)": "@/components/Theme$1.vue" // 匹配components目录内的主题vue文件
    }
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarBackgroundColor": "#fff"
  },
  "tabBar": {
    "custom": true,
    "selectedColor": "#0E0E0E",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "/static/img/home.png",
        "selectedIconPath": "/static/img/home-selected-dark.png"
      },
      {
        "pagePath": "pages/index/bus", // 设置场馆首页为tabbar页面  防止switchTab跳其它tabbar页面之后场馆首页被关闭
        "text": "场馆首页",
        "iconPath": "/static/img/home.png",
        "selectedIconPath": "/static/img/home-selected-dark.png"
      },
      {
        "pagePath": "pages/class/class",
        "text": "约课",
        "iconPath": "/static/img/reserve.png",
        "selectedIconPath": "/static/img/reserve-selected-dark.png"
      },
      {
        "pagePath": "pages/mall/list",
        "text": "商城",
        "iconPath": "/static/img/reserve.png",
        "selectedIconPath": "/static/img/reserve-selected-dark.png"
      },
      {
        "pagePath": "pages/mall/list-new",
        "text": "积分商城",
        "iconPath": "/static/img/reserve.png",
        "selectedIconPath": "/static/img/reserve-selected-dark.png"
      },
      {
        "pagePath": "pages/my/index",
        "text": "我的",
        "iconPath": "/static/img/my.png",
        "selectedIconPath": "/static/img/my-selected-dark.png"
      }
    ]
  },
  "navigateToMiniProgramAppIdList": ["wx59254b8ea3bff684", "wx4615dc2ed15a6796"],
}
