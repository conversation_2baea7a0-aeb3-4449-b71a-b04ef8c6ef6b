<template>
  <view class="page-container">
    <!-- Main Content Area -->
    <view class="main-content">
      <!-- Category Sidebar -->
      <view class="category-sidebar">
        <view v-for="(category, index) in categories" :key="index" class="category-item"
          :class="{ active: selectedCategory === index }" @tap="selectCategory(index)">
          <text class="category-text">{{ category.name }}</text>
        </view>
      </view>

      <!-- Products Grid -->
      <view class="products-container">
        <!-- Top Points Section -->
        <view class="points-section">
          <view class="points-card">
            <view class="points-display">
              <text class="points-number">{{ currentPoint }}</text>
              <text class="points-text">积分</text>
            </view>
            <view class="points-actions">
              <view class="action-item" @tap="goDetail">
                <text class="action-text">积分详情</text>
                <text class="action-arrow">></text>
              </view>
              <view class="action-item" @tap="goUrlPage(`/pages/my/pointTask?bus_id=${busId}`)">
                <text class="action-text">获取更多积分</text>
                <text class="action-arrow">></text>
              </view>
            </view>
          </view>
        </view>
        <!-- Notice Section -->
        <view v-if="expPoint" class="notice-section" @tap="goUrlPage(`/pages/my/point?bus_id=${busId}`)">
          <image class="notice-icon" src="/static/img/tongzhi.png" />
          <text class="notice-text">您有{{ expPoint }}积分将于24小时内到期</text>
        </view>
        <view class="products-scroll">
          <z-paging ref="openPaging" v-model="products" :fixed="false" :refresher-enabled="false"
            :show-loading-more-no-more-view="products.length > 10 ? true : false"
            :empty-view-img-style="{ width: '103rpx', height: '144rpx' }" empty-view-text="暂无商品数据"
            empty-view-img="https://imagecdn.rocketbird.cn/minprogram/alipay-member/not-buy.png">
            <view class="products-grid">
              <view v-for="product in products" :key="product.id" class="product-item" @tap="goToProductDetail(product)">
                <image class="product-image" :src="product.goods_image" mode="aspectFill" />
                <view class="product-info">
                  <text class="product-name">{{ product.goods_name }}</text>
                  <text class="product-stock">库存 {{ product.last_volume }}</text>
                  <view v-if="categoryId === 'score'" class="product-price">
                    <text class="price-symbol">∮</text>
                    <text class="price-number">{{ product.point }}</text>
                  </view>
                  <view v-else class="product-price">
                    <text class="price-symbol">¥</text>
                    <text class="price-number">{{ product.commodity_price }}</text>
                  </view>
                </view>
              </view>
            </view>
          </z-paging>
        </view>
      </view>
    </view>

    <!-- Bottom Tab Bar -->
    <CustomTabsBar />

    <!-- Invitation Modal -->
    <InvitationModal :type="2" />
  </view>
</template>

<script setup lang="ts" name="MallListNew">
import InvitationModal from '@/components/InvitationModal.vue'
import CustomTabsBar from '@/components/custom-tabs-bar/index.vue'
import { usePoint } from '@/hooks/usePoint'
import { useUserStore } from '@/store/user'
import { useThemeStore } from '@/store/theme'
import { goUrlPage } from '@/utils'
import http from '@/utils/request'

const busId = ref('')
const userStore = useUserStore()
const selectedCategory = ref(0)
const { currentPoint, getUserPointAndExpire, expPoint } = usePoint()
const themeStore = useThemeStore()

// Categories data
const categories = ref<any>([])
const categoryId = computed(() => {
  return categories.value[selectedCategory.value]?.category_id || ''
})
const getCategories = () => {
  http.get('/Good/getCategoryList', {
    bus_id: busId.value || userStore.userInfoBusId,
  }).then((res) => {
    const arr = res.data || []
    categories.value = [{ category_id: '', name: '全部商品' }, ...arr, { category_id: 'score', name: '积分专区' }]
  })
}

// Methods
function getMallConfig() {
  themeStore.getConfig({ type: 10 }).then((res) => {
    busId.value = themeStore.theme10?.bus_id || ''
  })
}

function selectCategory(index: number) {
  selectedCategory.value = index
  // 积分专区请求原来的接口
  if (categoryId.value === 'score') {
    getScoreList() // Fetch score goods list
  } else {
    getList() // Refresh product list based on selected category
  }
}

function goToProductDetail(product: any) {
  goUrlPage(`/pages/mall/detail?id=${product.id}&type=2&bus_id=${busId.value}`)
}

async function goDetail() {
  if (userStore.userInfoUserId) {
    goUrlPage(`/pages/my/point?bus_id=${busId.value}`)
  } else {
    uni.navigateTo({
      url: '/pages/login?hasChecked=true&navigateBack=true',
    })
  }
}

const openPaging = ref()
const products = ref<any>([])
function getList() {
  http
    .get('/Good/getGoodList', {
      bus_id: busId.value || userStore.userInfoBusId,
      category_id: categoryId.value,
    })
    .then(async (res) => {
      if (!openPaging.value) {
        await nextTick()
      }

      openPaging.value.reload()
      openPaging.value.setLocalPaging(res.data)
    })
    .catch(async () => {
      if (!openPaging.value) {
        await nextTick()
      }

      openPaging.value.setLocalPaging([], false)
    })
}
function getScoreList() {
  http.get('/Pointmall/getPointGoodsList', {
    bus_id: busId.value || userStore.userInfoBusId,
    type: 2, // Assuming type 2 is for point goods
  }).then(async (res) => {
    if (!openPaging.value) {
      await nextTick()
    }

    openPaging.value.reload()
    openPaging.value.setLocalPaging(res.data.list)
  })
  .catch(async () => {
    if (!openPaging.value) {
      await nextTick()
    }

    openPaging.value.setLocalPaging([], false)
  })
}

// Lifecycle hooks
onLoad(() => {
  // Initialize
})

onShow(() => {
  getMallConfig()
  getUserPointAndExpire()
  getCategories()
  getList()
})
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// Points Section
.points-section {
  margin-bottom: 20rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

  // Decorative diamond element - more subtle and integrated
  &::after {
    content: '';
    position: absolute;
    top: 30rpx;
    right: 30rpx;
    width: 50rpx;
    height: 50rpx;
    background: linear-gradient(135deg, #ff9a56, #ffb366);
    transform: rotate(45deg);
    border-radius: 6rpx;
    opacity: 0.9;
  }

  .points-card {
    background: transparent;
    border-radius: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;

    .points-display {
      display: flex;
      align-items: baseline;

      .points-number {
        font-size: 100rpx;
        font-weight: bold;
        color: #333333;
        line-height: 1;
      }

      .points-text {
        font-size: 28rpx;
        color: #666666;
        margin-left: 8rpx;
        font-weight: 500;
      }
    }

    .points-actions {
      display: flex;
      justify-content: space-between;
      gap: 16rpx;
      margin-top: 10rpx;

      .action-item {
        display: flex;
        align-items: center;
        background: rgba(255, 116, 39, 0.08);
        padding: 12rpx 16rpx;
        border-radius: 20rpx;

        .action-text {
          font-size: 26rpx;
          color: #ff7427;
          margin-right: 8rpx;
          font-weight: 500;
        }

        .action-arrow {
          font-size: 22rpx;
          color: #ff7427;
          font-weight: bold;
        }
      }
    }
  }
}

// Notice Section
.notice-section {
  background: rgba(255, 116, 39, 0.1);
  border-radius: 10rpx;
  padding: 15rpx 20rpx;
  display: flex;
  align-items: center;

  .notice-icon {
    width: 28rpx;
    height: 28rpx;
    margin-right: 15rpx;
  }

  .notice-text {
    font-size: 24rpx;
    color: #ff7427;
    font-weight: 500;
  }
}

// Main Content
.main-content {
  flex: 1;
  display: flex;
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

// Category Sidebar
.category-sidebar {
  width: 200rpx;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;

  .category-item {
    padding: 30rpx 20rpx;
    border-bottom: 1rpx solid #eeeeee;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &.active {
      background: white;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 40rpx;
        background: #ff7427;
        border-radius: 0 3rpx 3rpx 0;
      }

      .category-text {
        color: #ff7427;
        font-weight: 500;
      }
    }

    .category-text {
      font-size: 28rpx;
      color: #333333;
      text-align: center;
    }
  }
}

// Products Container
.products-container {
  flex: 1;
  padding: 30rpx;

  .products-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .product-item {
      width: 48%;
      margin-bottom: 20rpx;
      background: white;
      border-radius: 15rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

      .product-image {
        width: 100%;
        height: 200rpx;
      }

      .product-info {
        padding: 20rpx;

        .product-name {
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
          display: block;
          margin-bottom: 10rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .product-stock {
          font-size: 22rpx;
          color: #999999;
          display: block;
          margin-bottom: 15rpx;
        }

        .product-price {
          display: flex;
          align-items: baseline;

          .price-symbol {
            font-size: 20rpx;
            color: #ff7427;
          }

          .price-number {
            font-size: 32rpx;
            color: #ff7427;
            font-weight: bold;
          }
        }
      }
    }
  }
}

.products-scroll {
  margin-top: 20rpx;
  height: calc(100vh - 432rpx); // Adjust height based on header
}
</style>
