<template>
  <custom-tabs :model-value="current" @change="tabChange">
    <custom-tab-pane
      v-for="item in tabList"
      :id="item.type"
      :key="item.type"
      :label="item.label"
      :number="item.type === '1' ? canUseNumber : 0"
    >
      <view v-if="computedId !== ''" class="card-page">
        <!-- 会员凭证页面 -->
        <view v-if="computedId === '1'">
          <ticket />
        </view>

        <!-- 商品凭证页面 -->
        <view v-if="computedId === '2'">
          <!-- 筛选按钮 -->
          <view class="filter-tabs">
            <view
              v-for="filter in filterList"
              :key="filter.value"
              :class="['filter-tab', { active: currentFilter === filter.value }]"
              @tap="handleFilterChange(filter.value)"
            >
              {{ filter.label }}
            </view>
          </view>

          <!-- 商品凭证列表 -->
          <view class="voucher-list">
            <view
              v-for="voucher in filteredVoucherList"
              :key="voucher.id"
              class="voucher-item"
            >
              <view class="voucher-content">
                <image :src="voucher.image" class="voucher-image" mode="aspectFill" />
                <view class="voucher-info">
                  <view class="voucher-title">{{ voucher.title }}</view>
                  <view class="voucher-quantity">x{{ voucher.quantity }}</view>
                  <view class="voucher-date">{{ voucher.date }}</view>
                  <view class="voucher-store">门店：{{ voucher.store }}</view>
                </view>
              </view>
              <view class="voucher-action">
                <view class="action-btn" @tap="handleViewVoucher(voucher)">
                  查看商品凭证 >
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </custom-tab-pane>
  </custom-tabs>
</template>

<script setup lang="ts">
import http from '@/utils/request'
import customTabs from '@/components/custom-tabs/custom-tabs.vue'
import customTabPane from '@/components/custom-tabs/custom-tab-pane.vue'
import { useUserStore } from '@/store/user'
import { useLogin } from '@/hooks/useLogin'
import ticket from './ticket.vue'

const tabList = ref([
  { type: '1', label: '入场凭证' },
  { type: '2', label: '商品凭证' },
])

const filterList = ref([
  { value: 'pending', label: '待核销' },
  { value: 'verified', label: '已核销' },
])

const computedId = ref('')
const { checkLogin } = useLogin()
const current = ref(0)
const currentFilter = ref('pending')
const userStore = useUserStore()

onShow(() => {
  checkLogin()
})

const canUseNumber = ref(0)

onLoad(() => {
  getLockerNum()
  getVoucherList()
})

function tabChange(e: any) {
  current.value = e.value
  computedId.value = e.computedId
}

function handleFilterChange(filterValue: string) {
  currentFilter.value = filterValue
}

// 会员凭证相关逻辑
interface LockerInfo {
  bus_id: string
  user_id: string
  bus_name: string
  status: number,
  cabinet_list: Array<{cabinet_id: string, cabinet_name: string}>
  cabinet_ids: string,
  timestamp: string
}
const lockerInfo = ref<LockerInfo[]>([])

function getLockerNum() {
  if (!userStore.userInfoUserId) {
    return
  }
  http
    .get('Cabinets/getBusOccupyCabinet', {
      bus_id: userStore.userInfoBusId,
      user_id: userStore.userInfoUserId,
      m_id: userStore.userInfoMId,
      showToast: false,
    })
    .then((res) => {
      const list = res.data || []
      lockerInfo.value = list.map((item: any) => {
        return {
          ...item,
          cabinet_ids: item.cabinet_list.map((i: any) => i.cabinet_id).join('、'),
          timestamp: new Date().getTime(),
        }
      })
    })
    .catch((_err) => {
      lockerInfo.value = []
    })
}

function releaseLockerNum(info: LockerInfo) {
  http
    .get('Cabinets/releaseUserCabinet', {
      bus_id: info.bus_id,
      user_id: info.user_id,
    })
    .then((_res) => {
      getLockerNum()
    })
}

function handleLocker(info: LockerInfo) {
  if (info.status === 2) {
    uni.navigateTo({
      url: `/packageMy/cabinet/record`,
    })
  }
}

// 商品凭证相关逻辑
interface VoucherInfo {
  id: string
  title: string
  quantity: number
  date: string
  store: string
  image: string
  status: 'pending' | 'verified'
}

const voucherList = ref<VoucherInfo[]>([])

const filteredVoucherList = computed(() => {
  if (currentFilter.value === 'all') {
    return voucherList.value
  }
  return voucherList.value.filter(item => item.status === currentFilter.value)
})

function getVoucherList() {
  // 模拟数据，实际应该调用API
  voucherList.value = [
    {
      id: '1',
      title: '高乐士纯净水500ml',
      quantity: 1,
      date: '2024-10-12 12:32',
      store: '勤鸟运动平台',
      image: 'https://via.placeholder.com/120x120/f0f0f0/999999?text=商品',
      status: 'pending'
    },
    {
      id: '2',
      title: '高乐士纯净水字多的时候就...',
      quantity: 1,
      date: '2024-10-12 12:32',
      store: '勤鸟运动平台',
      image: 'https://via.placeholder.com/120x120/f0f0f0/999999?text=商品',
      status: 'pending'
    },
    {
      id: '3',
      title: '高乐士纯净水字多的时候就...',
      quantity: 1,
      date: '2024-10-12 12:32',
      store: '勤鸟运动平台',
      image: 'https://via.placeholder.com/120x120/f0f0f0/999999?text=商品',
      status: 'verified'
    },
    {
      id: '4',
      title: '高乐士纯净水字多的时候就...',
      quantity: 1,
      date: '2024-10-12 12:32',
      store: '勤鸟运动平台',
      image: 'https://via.placeholder.com/120x120/f0f0f0/999999?text=商品',
      status: 'verified'
    }
  ]
}

function handleViewVoucher(voucher: VoucherInfo) {
  // 处理查看商品凭证
  uni.showToast({
    title: `查看${voucher.title}的凭证`,
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.card-page {
  padding-top: 20rpx;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.normal-btn-min {
  width: 500rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  &.center {
    justify-content: center;
  }
}

.text-ellipsis {
  display: inline-block;
  margin: 10rpx;
  max-width: 600rpx;
  width: 600rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 筛选标签样式
.filter-tabs {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  background-color: #f5f7f9;
  color: #666;
  transition: all 0.3s ease;

  &.active {
    background-color: #a1ea2b;
    color: #fff;
    font-weight: bold;
  }
}

// 商品凭证列表样式
.voucher-list {
  padding: 0 30rpx;
}

.voucher-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.voucher-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.voucher-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.voucher-info {
  flex: 1;
  min-width: 0;
}

.voucher-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.voucher-quantity {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  text-align: right;
}

.voucher-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.voucher-store {
  font-size: 24rpx;
  color: #999;
}

.voucher-action {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
</style>