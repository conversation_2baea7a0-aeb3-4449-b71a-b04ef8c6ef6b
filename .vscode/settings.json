{"editor.tabSize": 2, "editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["typescript", "vue", "html", "json"], "editor.defaultFormatter": "esbenp.prettier-vscode", "json.format.enable": false, "files.associations": {"*.json": "jsonc"}, "files.eol": "\n", "cSpell.words": ["aliapp", "alipay", "alipays", "autoclose", "autocrlf", "autofocus", "automator", "automerge", "browserslist", "builtins", "camelcase", "channing", "commitlint", "d<PERSON><PERSON><PERSON>", "dcloudio", "degit", "<PERSON><PERSON><PERSON>", "eqeqeq", "errmsg", "esbenp", "<PERSON><PERSON><PERSON><PERSON>", "FILESYSTEMS", "gitee", "glup", "<PERSON><PERSON><PERSON>", "initconfig", "johnsoncodehk", "jue<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "lintstagedrc", "miniapp", "monokai", "nvue", "Persistedstate", "pinia", "Prefinem", "prettierignore", "quickapp", "riskgo", "scrolltolower", "sheight", "smartblue", "solt", "splashscreen", "Strat", "stylelint", "stylelintrc", "swidth", "<PERSON><PERSON><PERSON>", "uniapp", "unocss", "unplugin", "vite", "vuedx", "vuex", "weixin"], "[vue]": {"editor.defaultFormatter": "Vue.volar"}}